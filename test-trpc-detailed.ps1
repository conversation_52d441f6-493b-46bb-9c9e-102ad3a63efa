# 详细的 tRPC 端点测试脚本
# 加载 System.Web 程序集以使用 UrlEncode
Add-Type -AssemblyName System.Web

$BaseUrl = "http://uat.ds.lesso.com:4000"
$Origin = "http://localhost:8080"
$Email = "<EMAIL>"
$Password = "admin123456"
$ProjectId = "cmd2f20b00005p907gzt6ypdz"
$OrgId = "cmd2f1m6e0000p90728qdflay"

Write-Host "=== Langfuse tRPC 端点详细测试 ===" -ForegroundColor Green
Write-Host "Project ID: $ProjectId"
Write-Host "Organization ID: $OrgId"
Write-Host ""

# 1. 获取 CSRF Token
Write-Host "1. 获取 CSRF Token..." -ForegroundColor Yellow
try {
    $csrfResponse = Invoke-WebRequest -Uri "$BaseUrl/api/auth/csrf" `
        -Method GET `
        -Headers @{ "Origin" = $Origin } `
        -SessionVariable session `
        -UseBasicParsing
    
    $csrfData = $csrfResponse.Content | ConvertFrom-Json
    $csrfToken = $csrfData.csrfToken
    Write-Host "✓ CSRF Token: $($csrfToken.Substring(0,10))..." -ForegroundColor Green
} catch {
    Write-Host "✗ 获取 CSRF Token 失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 2. 用户登录
Write-Host "2. 用户登录..." -ForegroundColor Yellow
try {
    $loginBody = "email=$Email&password=$Password&csrfToken=$csrfToken&callbackUrl=$BaseUrl/"
    
    $loginResponse = Invoke-WebRequest -Uri "$BaseUrl/api/auth/callback/credentials" `
        -Method POST `
        -Headers @{
            "Origin" = $Origin
            "Content-Type" = "application/x-www-form-urlencoded"
        } `
        -Body $loginBody `
        -WebSession $session `
        -UseBasicParsing
    
    Write-Host "✓ 登录状态码: $($loginResponse.StatusCode)" -ForegroundColor Green
} catch {
    Write-Host "✗ 登录失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 3. 验证会话
Write-Host "3. 验证会话..." -ForegroundColor Yellow
try {
    $sessionResponse = Invoke-WebRequest -Uri "$BaseUrl/api/auth/session" `
        -Method GET `
        -Headers @{ "Origin" = $Origin } `
        -WebSession $session `
        -UseBasicParsing
    
    $sessionData = $sessionResponse.Content | ConvertFrom-Json
    if ($sessionData.user) {
        Write-Host "✓ 用户已登录: $($sessionData.user.email)" -ForegroundColor Green
    } else {
        Write-Host "✗ 用户未登录" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "✗ 验证会话失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "=== 开始测试 tRPC 端点 ===" -ForegroundColor Cyan

# 4.1 测试 sessions.all
Write-Host "4.1 测试 sessions.all..." -ForegroundColor Yellow
try {
    # 构建输入参数：{"0":{"json":{"projectId":"cmd2f20b00005p907gzt6ypdz"}}}
    $sessionsInput = @{
        "0" = @{
            "json" = @{
                "projectId" = $ProjectId
            }
        }
    } | ConvertTo-Json -Compress -Depth 10
    $sessionsInputEncoded = [System.Web.HttpUtility]::UrlEncode($sessionsInput)
    
    $sessionsUrl = "$BaseUrl/api/trpc/sessions.all?batch=1&input=$sessionsInputEncoded"
    $sessionsResponse = Invoke-WebRequest -Uri $sessionsUrl `
        -Method GET `
        -Headers @{
            "Origin" = $Origin
            "Content-Type" = "application/json"
        } `
        -WebSession $session `
        -UseBasicParsing
    
    Write-Host "✓ Sessions 成功 - 状态码: $($sessionsResponse.StatusCode)" -ForegroundColor Green
    $sessionsData = $sessionsResponse.Content | ConvertFrom-Json
    if ($sessionsData[0].result.data.json) {
        $sessionCount = $sessionsData[0].result.data.json.Count
        Write-Host "  找到 $sessionCount 个会话" -ForegroundColor Gray
    }
} catch {
    Write-Host "✗ Sessions 失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 4.2 测试 traces.all
Write-Host "4.2 测试 traces.all..." -ForegroundColor Yellow
try {
    $tracesInput = @{
        "0" = @{
            "json" = @{
                "projectId" = $ProjectId
                "page" = 0
                "limit" = 10
            }
        }
    } | ConvertTo-Json -Compress -Depth 10
    $tracesInputEncoded = [System.Web.HttpUtility]::UrlEncode($tracesInput)
    
    $tracesUrl = "$BaseUrl/api/trpc/traces.all?batch=1&input=$tracesInputEncoded"
    $tracesResponse = Invoke-WebRequest -Uri $tracesUrl `
        -Method GET `
        -Headers @{
            "Origin" = $Origin
            "Content-Type" = "application/json"
        } `
        -WebSession $session `
        -UseBasicParsing
    
    Write-Host "✓ Traces 成功 - 状态码: $($tracesResponse.StatusCode)" -ForegroundColor Green
    $tracesData = $tracesResponse.Content | ConvertFrom-Json
    if ($tracesData[0].result.data.json.traces) {
        $traceCount = $tracesData[0].result.data.json.traces.Count
        Write-Host "  找到 $traceCount 个追踪" -ForegroundColor Gray
    }
} catch {
    Write-Host "✗ Traces 失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 4.3 测试 projects.all
Write-Host "4.3 测试 projects.all..." -ForegroundColor Yellow
try {
    $projectsInput = @{
        "0" = @{
            "json" = $null
        }
    } | ConvertTo-Json -Compress -Depth 10
    $projectsInputEncoded = [System.Web.HttpUtility]::UrlEncode($projectsInput)
    
    $projectsUrl = "$BaseUrl/api/trpc/projects.all?batch=1&input=$projectsInputEncoded"
    $projectsResponse = Invoke-WebRequest -Uri $projectsUrl `
        -Method GET `
        -Headers @{
            "Origin" = $Origin
            "Content-Type" = "application/json"
        } `
        -WebSession $session `
        -UseBasicParsing
    
    Write-Host "✓ Projects 成功 - 状态码: $($projectsResponse.StatusCode)" -ForegroundColor Green
    $projectsData = $projectsResponse.Content | ConvertFrom-Json
    if ($projectsData[0].result.data.json) {
        $projectCount = $projectsData[0].result.data.json.Count
        Write-Host "  找到 $projectCount 个项目" -ForegroundColor Gray
        foreach ($project in $projectsData[0].result.data.json) {
            Write-Host "    - $($project.name) ($($project.id))" -ForegroundColor Gray
        }
    }
} catch {
    Write-Host "✗ Projects 失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 4.4 测试 organizations.all
Write-Host "4.4 测试 organizations.all..." -ForegroundColor Yellow
try {
    $orgsInput = @{
        "0" = @{
            "json" = $null
        }
    } | ConvertTo-Json -Compress -Depth 10
    $orgsInputEncoded = [System.Web.HttpUtility]::UrlEncode($orgsInput)
    
    $orgsUrl = "$BaseUrl/api/trpc/organizations.all?batch=1&input=$orgsInputEncoded"
    $orgsResponse = Invoke-WebRequest -Uri $orgsUrl `
        -Method GET `
        -Headers @{
            "Origin" = $Origin
            "Content-Type" = "application/json"
        } `
        -WebSession $session `
        -UseBasicParsing
    
    Write-Host "✓ Organizations 成功 - 状态码: $($orgsResponse.StatusCode)" -ForegroundColor Green
    $orgsData = $orgsResponse.Content | ConvertFrom-Json
    if ($orgsData[0].result.data.json) {
        $orgCount = $orgsData[0].result.data.json.Count
        Write-Host "  找到 $orgCount 个组织" -ForegroundColor Gray
        foreach ($org in $orgsData[0].result.data.json) {
            Write-Host "    - $($org.name) ($($org.id))" -ForegroundColor Gray
        }
    }
} catch {
    Write-Host "✗ Organizations 失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "=== 测试完成 ===" -ForegroundColor Green
