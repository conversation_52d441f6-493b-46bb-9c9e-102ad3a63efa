# Langfuse CORS 测试脚本 (PowerShell)
$BaseUrl = "http://localhost:4000"
$Origin = "http://localhost:8080"

Write-Host "=== Langfuse CORS 测试 ===" -ForegroundColor Green
Write-Host "Base URL: $BaseUrl"
Write-Host "Origin: $Origin"
Write-Host ""

# 1. 测试预检请求
Write-Host "1. 测试 OPTIONS 预检请求..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "$BaseUrl/api/trpc/sessions.all" `
        -Method OPTIONS `
        -Headers @{
            "Origin" = $Origin
            "Access-Control-Request-Method" = "POST"
            "Access-Control-Request-Headers" = "Content-Type,Authorization"
        } `
        -UseBasicParsing
    
    Write-Host "状态码: $($response.StatusCode)"
    Write-Host "CORS 头信息:"
    $response.Headers.GetEnumerator() | Where-Object { $_.Key -like "*Access-Control*" } | ForEach-Object {
        Write-Host "  $($_.Key): $($_.Value)"
    }
} catch {
    Write-Host "错误: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# 2. 测试健康检查
Write-Host "2. 测试健康检查端点..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "$BaseUrl/api/health" `
        -Method GET `
        -Headers @{
            "Origin" = $Origin
            "Content-Type" = "application/json"
        } `
        -UseBasicParsing
    
    Write-Host "状态码: $($response.StatusCode)"
    Write-Host "CORS 头信息:"
    $response.Headers.GetEnumerator() | Where-Object { $_.Key -like "*Access-Control*" } | ForEach-Object {
        Write-Host "  $($_.Key): $($_.Value)"
    }
} catch {
    Write-Host "错误: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# 3. 测试 tRPC 端点
Write-Host "3. 测试 tRPC 端点..." -ForegroundColor Yellow
try {
    $url = "$BaseUrl/api/trpc/sessions.all?batch=1&input=%7B%220%22%3A%7B%22json%22%3Anull%7D%7D"
    $response = Invoke-WebRequest -Uri $url `
        -Method GET `
        -Headers @{
            "Origin" = $Origin
            "Content-Type" = "application/json"
        } `
        -UseBasicParsing
    
    Write-Host "状态码: $($response.StatusCode)"
    Write-Host "CORS 头信息:"
    $response.Headers.GetEnumerator() | Where-Object { $_.Key -like "*Access-Control*" } | ForEach-Object {
        Write-Host "  $($_.Key): $($_.Value)"
    }
} catch {
    Write-Host "错误: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# 4. 测试公共 ingestion 端点
Write-Host "4. 测试公共 ingestion 端点..." -ForegroundColor Yellow
try {
    $body = '{"batch":[]}'
    $response = Invoke-WebRequest -Uri "$BaseUrl/api/public/ingestion" `
        -Method POST `
        -Headers @{
            "Origin" = $Origin
            "Content-Type" = "application/json"
        } `
        -Body $body `
        -UseBasicParsing
    
    Write-Host "状态码: $($response.StatusCode)"
    Write-Host "CORS 头信息:"
    $response.Headers.GetEnumerator() | Where-Object { $_.Key -like "*Access-Control*" } | ForEach-Object {
        Write-Host "  $($_.Key): $($_.Value)"
    }
} catch {
    Write-Host "错误: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "=== 测试完成 ===" -ForegroundColor Green
Write-Host "检查响应头中是否包含："
Write-Host "- Access-Control-Allow-Origin: $Origin"
Write-Host "- Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS"
Write-Host "- Access-Control-Allow-Headers: ..."
Write-Host "- Access-Control-Allow-Credentials: true"
