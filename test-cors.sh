#!/bin/bash

# Langfuse CORS 测试脚本
BASE_URL="http://localhost:4000"
ORIGIN="http://localhost:8080"

echo "=== Langfuse CORS 测试 ==="
echo "Base URL: $BASE_URL"
echo "Origin: $ORIGIN"
echo ""

# 1. 测试预检请求
echo "1. 测试 OPTIONS 预检请求..."
curl -X OPTIONS \
  -H "Origin: $ORIGIN" \
  -H "Access-Control-Request-Method: POST" \
  -H "Access-Control-Request-Headers: Content-Type,Authorization" \
  -s -D - \
  "$BASE_URL/api/trpc/sessions.all" | head -20

echo -e "\n"

# 2. 测试健康检查
echo "2. 测试健康检查端点..."
curl -X GET \
  -H "Origin: $ORIGIN" \
  -H "Content-Type: application/json" \
  -s -D - \
  "$BASE_URL/api/health" | head -20

echo -e "\n"

# 3. 测试 tRPC 端点
echo "3. 测试 tRPC 端点..."
curl -X GET \
  -H "Origin: $ORIGIN" \
  -H "Content-Type: application/json" \
  -s -D - \
  "$BASE_URL/api/trpc/sessions.all?batch=1&input=%7B%220%22%3A%7B%22json%22%3Anull%7D%7D" | head -20

echo -e "\n"

# 4. 测试公共 ingestion 端点
echo "4. 测试公共 ingestion 端点..."
curl -X POST \
  -H "Origin: $ORIGIN" \
  -H "Content-Type: application/json" \
  -d '{"batch":[]}' \
  -s -D - \
  "$BASE_URL/api/public/ingestion" | head -20

echo -e "\n"

# 5. 测试认证端点
echo "5. 测试认证端点..."
curl -X GET \
  -H "Origin: $ORIGIN" \
  -H "Content-Type: application/json" \
  -s -D - \
  "$BASE_URL/api/auth/session" | head -20

echo -e "\n"

echo "=== 测试完成 ==="
echo "检查响应头中是否包含："
echo "- Access-Control-Allow-Origin: $ORIGIN"
echo "- Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS"
echo "- Access-Control-Allow-Headers: ..."
echo "- Access-Control-Allow-Credentials: true"
