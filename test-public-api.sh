#!/bin/bash

# 使用 API 密钥访问公共端点
BASE_URL="http://uat.ds.lesso.com:4000"
ORIGIN="http://localhost:8080"
PUBLIC_KEY="pk-lf-aee24b42-c87d-4b50-baaa-2c906d281b2c"
SECRET_KEY="sk-lf-7685a924-e518-4a7e-aa1b-35b0e5c94d9e"

echo "=== 使用 API 密钥访问公共端点 ==="

# 1. 测试 ingestion 端点（创建 trace）
echo "1. 测试 ingestion 端点..."
TIMESTAMP=$(date -u +"%Y-%m-%dT%H:%M:%S.%3NZ")
TRACE_ID="test-trace-$(date +%s)"

INGESTION_RESPONSE=$(curl -s \
  -X POST \
  -H "Origin: $ORIGIN" \
  -H "Content-Type: application/json" \
  -u "$PUBLIC_KEY:$SECRET_KEY" \
  -w "HTTP Status: %{http_code}\n" \
  -d "{
    \"batch\": [
      {
        \"id\": \"$TRACE_ID\",
        \"type\": \"trace-create\",
        \"timestamp\": \"$TIMESTAMP\",
        \"body\": {
          \"id\": \"$TRACE_ID\",
          \"name\": \"test-trace\",
          \"userId\": \"test-user\",
          \"metadata\": {
            \"source\": \"curl-test\"
          }
        }
      }
    ]
  }" \
  "$BASE_URL/api/public/ingestion")

echo "Ingestion 响应:"
echo "$INGESTION_RESPONSE"
echo ""

# 2. 测试获取 traces（如果支持）
echo "2. 测试获取 traces..."
TRACES_RESPONSE=$(curl -s \
  -H "Origin: $ORIGIN" \
  -H "Content-Type: application/json" \
  -u "$PUBLIC_KEY:$SECRET_KEY" \
  -w "HTTP Status: %{http_code}\n" \
  "$BASE_URL/api/public/traces")

echo "Traces 响应:"
echo "$TRACES_RESPONSE"
echo ""

# 3. 测试获取项目信息
echo "3. 测试获取项目信息..."
PROJECT_RESPONSE=$(curl -s \
  -H "Origin: $ORIGIN" \
  -H "Content-Type: application/json" \
  -u "$PUBLIC_KEY:$SECRET_KEY" \
  -w "HTTP Status: %{http_code}\n" \
  "$BASE_URL/api/public/projects")

echo "Project 响应:"
echo "$PROJECT_RESPONSE"
echo ""

# 4. 测试健康检查
echo "4. 测试健康检查..."
HEALTH_RESPONSE=$(curl -s \
  -H "Origin: $ORIGIN" \
  -w "HTTP Status: %{http_code}\n" \
  "$BASE_URL/api/health")

echo "Health 响应:"
echo "$HEALTH_RESPONSE"
