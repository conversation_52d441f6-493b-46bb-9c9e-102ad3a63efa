#!/bin/bash

# 使用会话认证访问 tRPC 端点
BASE_URL="http://uat.ds.lesso.com:4000"
ORIGIN="http://localhost:8080"
EMAIL="<EMAIL>"
PASSWORD="admin123456"
COOKIE_FILE="session_cookies.txt"

echo "=== 使用会话认证访问 tRPC 端点 ==="

# 清理之前的 cookie
rm -f $COOKIE_FILE

# 1. 获取 CSRF Token
echo "1. 获取 CSRF Token..."
CSRF_RESPONSE=$(curl -s -c $COOKIE_FILE -b $COOKIE_FILE \
  -H "Origin: $ORIGIN" \
  "$BASE_URL/api/auth/csrf")

CSRF_TOKEN=$(echo $CSRF_RESPONSE | jq -r '.csrfToken' 2>/dev/null || echo $CSRF_RESPONSE | grep -o '"csrfToken":"[^"]*"' | cut -d'"' -f4)
echo "CSRF Token: $CSRF_TOKEN"

# 2. 登录获取会话
echo "2. 登录获取会话..."
curl -s -c $COOKIE_FILE -b $COOKIE_FILE \
  -X POST \
  -H "Origin: $ORIGIN" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "email=$EMAIL&password=$PASSWORD&csrfToken=$CSRF_TOKEN&callbackUrl=$BASE_URL/" \
  "$BASE_URL/api/auth/callback/credentials" > /dev/null

# 3. 验证会话
echo "3. 验证会话..."
SESSION_CHECK=$(curl -s -b $COOKIE_FILE \
  -H "Origin: $ORIGIN" \
  "$BASE_URL/api/auth/session")
echo "会话状态: $SESSION_CHECK"

# 4. 使用会话访问 tRPC 端点
echo "4. 访问 tRPC sessions.all 端点..."
TRPC_RESPONSE=$(curl -s -b $COOKIE_FILE \
  -H "Origin: $ORIGIN" \
  -H "Content-Type: application/json" \
  -w "HTTP Status: %{http_code}\n" \
  "$BASE_URL/api/trpc/sessions.all?batch=1&input=%7B%220%22%3A%7B%22json%22%3Anull%7D%7D")

echo "tRPC 响应:"
echo "$TRPC_RESPONSE"

# 清理
rm -f $COOKIE_FILE
