#!/bin/bash

# 使用会话认证访问 tRPC 端点
BASE_URL="http://uat.ds.lesso.com:4000"
ORIGIN="http://localhost:8080"
EMAIL="<EMAIL>"
PASSWORD="admin123456"
COOKIE_FILE="session_cookies.txt"
PROJECT_ID="cmd2f20b00005p907gzt6ypdz"
ORG_ID="cmd2f1m6e0000p90728qdflay"

echo "=== 使用会话认证访问 tRPC 端点 ==="
echo "Project ID: $PROJECT_ID"
echo "Organization ID: $ORG_ID"

# 清理之前的 cookie
rm -f $COOKIE_FILE

# 1. 获取 CSRF Token
echo "1. 获取 CSRF Token..."
CSRF_RESPONSE=$(curl -s -c $COOKIE_FILE -b $COOKIE_FILE \
  -H "Origin: $ORIGIN" \
  "$BASE_URL/api/auth/csrf")

CSRF_TOKEN=$(echo $CSRF_RESPONSE | jq -r '.csrfToken' 2>/dev/null || echo $CSRF_RESPONSE | grep -o '"csrfToken":"[^"]*"' | cut -d'"' -f4)
echo "CSRF Token: $CSRF_TOKEN"

# 2. 登录获取会话
echo "2. 登录获取会话..."
curl -s -c $COOKIE_FILE -b $COOKIE_FILE \
  -X POST \
  -H "Origin: $ORIGIN" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "email=$EMAIL&password=$PASSWORD&csrfToken=$CSRF_TOKEN&callbackUrl=$BASE_URL/" \
  "$BASE_URL/api/auth/callback/credentials" > /dev/null

# 3. 验证会话
echo "3. 验证会话..."
SESSION_CHECK=$(curl -s -b $COOKIE_FILE \
  -H "Origin: $ORIGIN" \
  "$BASE_URL/api/auth/session")
echo "会话状态: $SESSION_CHECK"

# 4. 测试多个 tRPC 端点
echo "4. 测试多个 tRPC 端点..."
echo ""

# 4.1 测试 sessions.all（需要 projectId）
echo "4.1 测试 sessions.all 端点..."
# URL 编码的输入：{"0":{"json":{"projectId":"cmd2f20b00005p907gzt6ypdz"}}}
SESSIONS_INPUT="%7B%220%22%3A%7B%22json%22%3A%7B%22projectId%22%3A%22$PROJECT_ID%22%7D%7D%7D"
SESSIONS_RESPONSE=$(curl -s -b "$COOKIE_FILE" \
  -H "Origin: $ORIGIN" \
  -H "Content-Type: application/json" \
  -w "HTTP Status: %{http_code}\n" \
  "$BASE_URL/api/trpc/sessions.all?batch=1&input=$SESSIONS_INPUT")

echo "Sessions 响应:"
echo "$SESSIONS_RESPONSE"
echo ""

# 4.2 测试 traces.all（需要 projectId）
echo "4.2 测试 traces.all 端点..."
TRACES_INPUT="%7B%220%22%3A%7B%22json%22%3A%7B%22projectId%22%3A%22$PROJECT_ID%22%7D%7D%7D"
TRACES_RESPONSE=$(curl -s -b "$COOKIE_FILE" \
  -H "Origin: $ORIGIN" \
  -H "Content-Type: application/json" \
  -w "HTTP Status: %{http_code}\n" \
  "$BASE_URL/api/trpc/traces.all?batch=1&input=$TRACES_INPUT")

echo "Traces 响应:"
echo "$TRACES_RESPONSE"
echo ""

# 4.3 测试 projects.all（获取项目列表）
echo "4.3 测试 projects.all 端点..."
PROJECTS_INPUT="%7B%220%22%3A%7B%22json%22%3Anull%7D%7D"
PROJECTS_RESPONSE=$(curl -s -b "$COOKIE_FILE" \
  -H "Origin: $ORIGIN" \
  -H "Content-Type: application/json" \
  -w "HTTP Status: %{http_code}\n" \
  "$BASE_URL/api/trpc/projects.all?batch=1&input=$PROJECTS_INPUT")

echo "Projects 响应:"
echo "$PROJECTS_RESPONSE"
echo ""

# 4.4 测试 organizations.all（获取组织列表）
echo "4.4 测试 organizations.all 端点..."
ORGS_INPUT="%7B%220%22%3A%7B%22json%22%3Anull%7D%7D"
ORGS_RESPONSE=$(curl -s -b "$COOKIE_FILE" \
  -H "Origin: $ORIGIN" \
  -H "Content-Type: application/json" \
  -w "HTTP Status: %{http_code}\n" \
  "$BASE_URL/api/trpc/organizations.all?batch=1&input=$ORGS_INPUT")

echo "Organizations 响应:"
echo "$ORGS_RESPONSE"
echo ""

# 4.5 测试 dashboard.chart（仪表板数据）
echo "4.5 测试 dashboard.chart 端点..."
DASHBOARD_INPUT="%7B%220%22%3A%7B%22json%22%3A%7B%22projectId%22%3A%22$PROJECT_ID%22%2C%22from%22%3A%22$(date -d '7 days ago' -u +%Y-%m-%dT%H:%M:%S.000Z)%22%2C%22to%22%3A%22$(date -u +%Y-%m-%dT%H:%M:%S.000Z)%22%7D%7D%7D"
DASHBOARD_RESPONSE=$(curl -s -b "$COOKIE_FILE" \
  -H "Origin: $ORIGIN" \
  -H "Content-Type: application/json" \
  -w "HTTP Status: %{http_code}\n" \
  "$BASE_URL/api/trpc/dashboard.chart?batch=1&input=$DASHBOARD_INPUT")

echo "Dashboard 响应:"
echo "$DASHBOARD_RESPONSE"
echo ""

# 清理
rm -f "$COOKIE_FILE"

echo "=== 测试完成 ==="
echo ""
echo "说明："
echo "- sessions.all: 获取项目中的会话列表"
echo "- traces.all: 获取项目中的追踪列表"
echo "- projects.all: 获取用户可访问的项目列表"
echo "- organizations.all: 获取用户可访问的组织列表"
echo "- dashboard.chart: 获取仪表板图表数据"
