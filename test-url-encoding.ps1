# URL 编码测试和示例
Add-Type -AssemblyName System.Web

$ProjectId = "cmd2f20b00005p907gzt6ypdz"

Write-Host "=== tRPC URL 编码示例 ===" -ForegroundColor Green
Write-Host ""

# 1. sessions.all 的输入参数
Write-Host "1. sessions.all 输入参数:" -ForegroundColor Yellow
$sessionsInput = @{
    "0" = @{
        "json" = @{
            "projectId" = $ProjectId
        }
    }
} | ConvertTo-Json -Compress -Depth 10

Write-Host "原始 JSON: $sessionsInput"
$sessionsEncoded = [System.Web.HttpUtility]::UrlEncode($sessionsInput)
Write-Host "URL 编码后: $sessionsEncoded"
Write-Host ""

# 2. traces.all 的输入参数
Write-Host "2. traces.all 输入参数:" -ForegroundColor Yellow
$tracesInput = @{
    "0" = @{
        "json" = @{
            "projectId" = $ProjectId
            "page" = 0
            "limit" = 10
        }
    }
} | ConvertTo-Json -Compress -Depth 10

Write-Host "原始 JSON: $tracesInput"
$tracesEncoded = [System.Web.HttpUtility]::UrlEncode($tracesInput)
Write-Host "URL 编码后: $tracesEncoded"
Write-Host ""

# 3. 生成完整的 curl 命令
Write-Host "3. 完整的 curl 命令示例:" -ForegroundColor Yellow
Write-Host ""

Write-Host "# sessions.all" -ForegroundColor Cyan
Write-Host "curl -b cookies.txt \\"
Write-Host "  -H `"Origin: http://localhost:8080`" \\"
Write-Host "  -H `"Content-Type: application/json`" \\"
Write-Host "  `"http://uat.ds.lesso.com:4000/api/trpc/sessions.all?batch=1&input=$sessionsEncoded`""
Write-Host ""

Write-Host "# traces.all" -ForegroundColor Cyan
Write-Host "curl -b cookies.txt \\"
Write-Host "  -H `"Origin: http://localhost:8080`" \\"
Write-Host "  -H `"Content-Type: application/json`" \\"
Write-Host "  `"http://uat.ds.lesso.com:4000/api/trpc/traces.all?batch=1&input=$tracesEncoded`""
Write-Host ""

# 4. 手动编码的版本（用于 bash 脚本）
Write-Host "4. 手动编码版本（用于 bash）:" -ForegroundColor Yellow
Write-Host ""

Write-Host "# sessions.all 手动编码" -ForegroundColor Cyan
$manualSessionsEncoded = "%7B%220%22%3A%7B%22json%22%3A%7B%22projectId%22%3A%22$ProjectId%22%7D%7D%7D"
Write-Host "SESSIONS_INPUT=`"$manualSessionsEncoded`""
Write-Host ""

Write-Host "# traces.all 手动编码" -ForegroundColor Cyan
$manualTracesEncoded = "%7B%220%22%3A%7B%22json%22%3A%7B%22projectId%22%3A%22$ProjectId%22%2C%22page%22%3A0%2C%22limit%22%3A10%7D%7D%7D"
Write-Host "TRACES_INPUT=`"$manualTracesEncoded`""
Write-Host ""

Write-Host "=== 编码对照表 ===" -ForegroundColor Green
Write-Host "{ -> %7B"
Write-Host "} -> %7D"
Write-Host "`" -> %22"
Write-Host ": -> %3A"
Write-Host ", -> %2C"
