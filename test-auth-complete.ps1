# 完整的 Langfuse 认证测试
$BaseUrl = "http://uat.ds.lesso.com:4000"
$Origin = "http://localhost:8080"
$Email = "<EMAIL>"
$Password = "admin123456"
$PublicKey = "pk-lf-aee24b42-c87d-4b50-baaa-2c906d281b2c"
$SecretKey = "sk-lf-7685a924-e518-4a7e-aa1b-35b0e5c94d9e"

Write-Host "=== Langfuse 完整认证测试 ===" -ForegroundColor Green
Write-Host ""

# ===== 第一部分：使用 API 密钥访问公共端点 =====
Write-Host "第一部分：使用 API 密钥访问公共端点" -ForegroundColor Cyan
Write-Host "Public Key: $PublicKey"
Write-Host "Secret Key: $($SecretKey.Substring(0,10))..."
Write-Host ""

# 1. 测试 ingestion 端点
Write-Host "1. 测试 ingestion 端点..." -ForegroundColor Yellow
try {
    $credentials = [Convert]::ToBase64String([Text.Encoding]::ASCII.GetBytes("${PublicKey}:${SecretKey}"))
    $timestamp = (Get-Date).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
    $traceId = "test-trace-$(Get-Date -Format 'yyyyMMddHHmmss')"
    
    $body = @{
        batch = @(
            @{
                id = $traceId
                type = "trace-create"
                timestamp = $timestamp
                body = @{
                    id = $traceId
                    name = "test-trace"
                    userId = "test-user"
                    metadata = @{
                        source = "powershell-test"
                    }
                }
            }
        )
    } | ConvertTo-Json -Depth 10
    
    $response = Invoke-WebRequest -Uri "$BaseUrl/api/public/ingestion" `
        -Method POST `
        -Headers @{
            "Origin" = $Origin
            "Content-Type" = "application/json"
            "Authorization" = "Basic $credentials"
        } `
        -Body $body `
        -UseBasicParsing
    
    Write-Host "✓ Ingestion 成功 - 状态码: $($response.StatusCode)" -ForegroundColor Green
    Write-Host "响应: $($response.Content.Substring(0, [Math]::Min(100, $response.Content.Length)))..."
} catch {
    Write-Host "✗ Ingestion 失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# 2. 测试健康检查
Write-Host "2. 测试健康检查..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "$BaseUrl/api/health" `
        -Method GET `
        -Headers @{ "Origin" = $Origin } `
        -UseBasicParsing
    
    Write-Host "✓ 健康检查成功 - 状态码: $($response.StatusCode)" -ForegroundColor Green
    Write-Host "响应: $($response.Content)"
} catch {
    Write-Host "✗ 健康检查失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "=" * 60
Write-Host ""

# ===== 第二部分：使用会话认证访问 tRPC 端点 =====
Write-Host "第二部分：使用会话认证访问 tRPC 端点" -ForegroundColor Cyan
Write-Host "Email: $Email"
Write-Host ""

# 1. 获取 CSRF Token
Write-Host "1. 获取 CSRF Token..." -ForegroundColor Yellow
try {
    $csrfResponse = Invoke-WebRequest -Uri "$BaseUrl/api/auth/csrf" `
        -Method GET `
        -Headers @{ "Origin" = $Origin } `
        -SessionVariable session `
        -UseBasicParsing
    
    $csrfData = $csrfResponse.Content | ConvertFrom-Json
    $csrfToken = $csrfData.csrfToken
    Write-Host "✓ CSRF Token 获取成功: $($csrfToken.Substring(0,10))..." -ForegroundColor Green
} catch {
    Write-Host "✗ 获取 CSRF Token 失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 2. 用户登录
Write-Host "2. 用户登录..." -ForegroundColor Yellow
try {
    $loginBody = "email=$Email&password=$Password&csrfToken=$csrfToken&callbackUrl=$BaseUrl/"
    
    $loginResponse = Invoke-WebRequest -Uri "$BaseUrl/api/auth/callback/credentials" `
        -Method POST `
        -Headers @{
            "Origin" = $Origin
            "Content-Type" = "application/x-www-form-urlencoded"
        } `
        -Body $loginBody `
        -WebSession $session `
        -UseBasicParsing
    
    Write-Host "✓ 登录请求发送 - 状态码: $($loginResponse.StatusCode)" -ForegroundColor Green
} catch {
    Write-Host "✗ 登录失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 3. 检查会话状态
Write-Host "3. 检查会话状态..." -ForegroundColor Yellow
try {
    $sessionResponse = Invoke-WebRequest -Uri "$BaseUrl/api/auth/session" `
        -Method GET `
        -Headers @{ "Origin" = $Origin } `
        -WebSession $session `
        -UseBasicParsing
    
    $sessionData = $sessionResponse.Content | ConvertFrom-Json
    if ($sessionData.user) {
        Write-Host "✓ 用户已登录: $($sessionData.user.email)" -ForegroundColor Green
        
        # 4. 测试 tRPC 调用
        Write-Host "4. 测试 tRPC sessions.all 调用..." -ForegroundColor Yellow
        try {
            $trpcUrl = "$BaseUrl/api/trpc/sessions.all?batch=1&input=%7B%220%22%3A%7B%22json%22%3Anull%7D%7D"
            $trpcResponse = Invoke-WebRequest -Uri $trpcUrl `
                -Method GET `
                -Headers @{
                    "Origin" = $Origin
                    "Content-Type" = "application/json"
                } `
                -WebSession $session `
                -UseBasicParsing
            
            Write-Host "✓ tRPC 调用成功 - 状态码: $($trpcResponse.StatusCode)" -ForegroundColor Green
            Write-Host "响应: $($trpcResponse.Content.Substring(0, [Math]::Min(200, $trpcResponse.Content.Length)))..."
        } catch {
            Write-Host "✗ tRPC 调用失败: $($_.Exception.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "✗ 用户未登录" -ForegroundColor Red
    }
} catch {
    Write-Host "✗ 检查会话失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "=== 测试完成 ===" -ForegroundColor Green
Write-Host ""
Write-Host "总结:" -ForegroundColor Cyan
Write-Host "- API 密钥适用于: /api/public/* 端点"
Write-Host "- 会话认证适用于: /api/trpc/* 端点"
Write-Host "- 两种认证方式不能互换使用"
