#!/bin/bash

# 简单的 tRPC 测试脚本 - 只测试最基本的端点
BASE_URL="http://uat.ds.lesso.com:4000"
ORIGIN="http://localhost:8080"
EMAIL="<EMAIL>"
PASSWORD="lesso2128@2025"
COOKIE_FILE="simple_cookies.txt"
PROJECT_ID="cmd2f20b00005p907gzt6ypdz"

echo "=== 简单 tRPC 测试 ==="
echo "Project ID: $PROJECT_ID"
echo ""

# 清理
rm -f "$COOKIE_FILE"

# 1. 登录
echo "1. 登录..."
CSRF_TOKEN=$(curl -s -c "$COOKIE_FILE" -H "Origin: $ORIGIN" "$BASE_URL/api/auth/csrf" | grep -o '"csrfToken":"[^"]*"' | cut -d'"' -f4)
curl -s -c "$COOKIE_FILE" -b "$COOKIE_FILE" -X POST \
  -H "Origin: $ORIGIN" -H "Content-Type: application/x-www-form-urlencoded" \
  -d "email=$EMAIL&password=$PASSWORD&csrfToken=$CSRF_TOKEN&callbackUrl=$BASE_URL/" \
  "$BASE_URL/api/auth/callback/credentials" > /dev/null

# 验证登录
USER_EMAIL=$(curl -s -b "$COOKIE_FILE" -H "Origin: $ORIGIN" "$BASE_URL/api/auth/session" | grep -o '"email":"[^"]*"' | cut -d'"' -f4)
if [ -n "$USER_EMAIL" ]; then
    echo "✓ 登录成功: $USER_EMAIL"
else
    echo "✗ 登录失败"
    exit 1
fi
echo ""

# 2. 测试 traces.all（最简单的参数）
echo "2. 测试 traces.all..."
# 参数：{"0":{"json":{"projectId":"xxx","searchQuery":"","searchType":[],"filter":[],"orderBy":{"column":"timestamp","order":"DESC"},"page":0,"limit":5}}}
TRACES_URL="$BASE_URL/api/trpc/traces.all?batch=1&input=%7B%220%22%3A%7B%22json%22%3A%7B%22projectId%22%3A%22$PROJECT_ID%22%2C%22searchQuery%22%3A%22%22%2C%22searchType%22%3A%5B%5D%2C%22filter%22%3A%5B%5D%2C%22orderBy%22%3A%7B%22column%22%3A%22timestamp%22%2C%22order%22%3A%22DESC%22%7D%2C%22page%22%3A0%2C%22limit%22%3A5%7D%7D%7D"

TRACES_RESPONSE=$(curl -s -b "$COOKIE_FILE" -H "Origin: $ORIGIN" -H "Content-Type: application/json" -w "STATUS:%{http_code}" "$TRACES_URL")
STATUS=$(echo "$TRACES_RESPONSE" | grep -o "STATUS:[0-9]*" | cut -d: -f2)
BODY=$(echo "$TRACES_RESPONSE" | sed 's/STATUS:[0-9]*$//')

echo "状态码: $STATUS"
if [ "$STATUS" = "200" ]; then
    echo "✓ traces.all 成功"
    # 尝试提取 traces 数量
    if echo "$BODY" | grep -q '"traces"'; then
        echo "  响应包含 traces 数据"
    else
        echo "  响应: $(echo "$BODY" | head -c 200)..."
    fi
else
    echo "✗ traces.all 失败"
    echo "  错误: $(echo "$BODY" | head -c 300)..."
fi
echo ""

# 3. 测试 sessions.all
echo "3. 测试 sessions.all..."
# 参数：{"0":{"json":{"projectId":"xxx","filter":[],"orderBy":{"column":"createdAt","order":"DESC"},"page":0,"limit":5}}}
SESSIONS_URL="$BASE_URL/api/trpc/sessions.all?batch=1&input=%7B%220%22%3A%7B%22json%22%3A%7B%22projectId%22%3A%22$PROJECT_ID%22%2C%22filter%22%3A%5B%5D%2C%22orderBy%22%3A%7B%22column%22%3A%22createdAt%22%2C%22order%22%3A%22DESC%22%7D%2C%22page%22%3A0%2C%22limit%22%3A5%7D%7D%7D"

SESSIONS_RESPONSE=$(curl -s -b "$COOKIE_FILE" -H "Origin: $ORIGIN" -H "Content-Type: application/json" -w "STATUS:%{http_code}" "$SESSIONS_URL")
STATUS=$(echo "$SESSIONS_RESPONSE" | grep -o "STATUS:[0-9]*" | cut -d: -f2)
BODY=$(echo "$SESSIONS_RESPONSE" | sed 's/STATUS:[0-9]*$//')

echo "状态码: $STATUS"
if [ "$STATUS" = "200" ]; then
    echo "✓ sessions.all 成功"
    echo "  响应: $(echo "$BODY" | head -c 200)..."
else
    echo "✗ sessions.all 失败"
    echo "  错误: $(echo "$BODY" | head -c 300)..."
fi
echo ""

# 4. 测试一个简单的端点（如果存在）
echo "4. 测试 users.me（获取当前用户信息）..."
# 参数：{"0":{"json":null}}
USERS_URL="$BASE_URL/api/trpc/users.me?batch=1&input=%7B%220%22%3A%7B%22json%22%3Anull%7D%7D"

USERS_RESPONSE=$(curl -s -b "$COOKIE_FILE" -H "Origin: $ORIGIN" -H "Content-Type: application/json" -w "STATUS:%{http_code}" "$USERS_URL")
STATUS=$(echo "$USERS_RESPONSE" | grep -o "STATUS:[0-9]*" | cut -d: -f2)
BODY=$(echo "$USERS_RESPONSE" | sed 's/STATUS:[0-9]*$//')

echo "状态码: $STATUS"
if [ "$STATUS" = "200" ]; then
    echo "✓ users.me 成功"
    echo "  响应: $(echo "$BODY" | head -c 200)..."
elif [ "$STATUS" = "404" ]; then
    echo "- users.me 端点不存在（正常）"
else
    echo "✗ users.me 失败"
    echo "  错误: $(echo "$BODY" | head -c 300)..."
fi
echo ""

# 清理
rm -f "$COOKIE_FILE"

echo "=== 测试完成 ==="
echo ""
echo "总结："
echo "- 如果 traces.all 和 sessions.all 都返回 200，说明 CORS 和认证配置正确"
echo "- 如果返回 400，说明参数格式仍有问题"
echo "- 如果返回 401，说明认证失败"
echo "- 如果返回 404，说明端点路径错误"
