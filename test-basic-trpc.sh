#!/bin/bash

# 基础 tRPC 端点测试脚本
BASE_URL="http://uat.ds.lesso.com:4000"
ORIGIN="http://localhost:8080"
EMAIL="<EMAIL>"
PASSWORD="lesso2128@2025"
COOKIE_FILE="basic_cookies.txt"
PROJECT_ID="cmd2f20b00005p907gzt6ypdz"

echo "=== 基础 tRPC 端点测试 ==="
echo "Project ID: $PROJECT_ID"
echo ""

# 清理之前的 cookie
rm -f "$COOKIE_FILE"

# 1. 获取 CSRF Token
echo "1. 获取 CSRF Token..."
CSRF_RESPONSE=$(curl -s -c "$COOKIE_FILE" -b "$COOKIE_FILE" \
  -H "Origin: $ORIGIN" \
  "$BASE_URL/api/auth/csrf")

CSRF_TOKEN=$(echo "$CSRF_RESPONSE" | jq -r '.csrfToken' 2>/dev/null || echo "$CSRF_RESPONSE" | grep -o '"csrfToken":"[^"]*"' | cut -d'"' -f4)
echo "CSRF Token: $CSRF_TOKEN"

# 2. 登录获取会话
echo "2. 登录获取会话..."
curl -s -c "$COOKIE_FILE" -b "$COOKIE_FILE" \
  -X POST \
  -H "Origin: $ORIGIN" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "email=$EMAIL&password=$PASSWORD&csrfToken=$CSRF_TOKEN&callbackUrl=$BASE_URL/" \
  "$BASE_URL/api/auth/callback/credentials" > /dev/null

# 3. 验证会话
echo "3. 验证会话..."
SESSION_CHECK=$(curl -s -b "$COOKIE_FILE" \
  -H "Origin: $ORIGIN" \
  "$BASE_URL/api/auth/session")
echo "会话验证: $(echo "$SESSION_CHECK" | jq -r '.user.email // "未登录"' 2>/dev/null || echo "会话检查失败")"
echo ""

# 4. 测试基础端点
echo "=== 测试基础端点 ==="

# 4.1 测试最简单的 traces.all
echo "4.1 测试 traces.all（最小参数）..."
# 最小参数集：{"0":{"json":{"projectId":"xxx","searchQuery":"","searchType":[],"filter":[],"orderBy":{"column":"timestamp","order":"desc"},"page":0,"limit":10}}}
TRACES_MINIMAL="%7B%220%22%3A%7B%22json%22%3A%7B%22projectId%22%3A%22$PROJECT_ID%22%2C%22searchQuery%22%3A%22%22%2C%22searchType%22%3A%5B%5D%2C%22filter%22%3A%5B%5D%2C%22orderBy%22%3A%7B%22column%22%3A%22timestamp%22%2C%22order%22%3A%22desc%22%7D%2C%22page%22%3A0%2C%22limit%22%3A10%7D%7D%7D"

TRACES_RESULT=$(curl -s -b "$COOKIE_FILE" \
  -H "Origin: $ORIGIN" \
  -H "Content-Type: application/json" \
  -w "HTTP_STATUS:%{http_code}" \
  "$BASE_URL/api/trpc/traces.all?batch=1&input=$TRACES_MINIMAL")

HTTP_STATUS=$(echo "$TRACES_RESULT" | grep -o "HTTP_STATUS:[0-9]*" | cut -d: -f2)
RESPONSE_BODY=$(echo "$TRACES_RESULT" | sed 's/HTTP_STATUS:[0-9]*$//')

echo "状态码: $HTTP_STATUS"
if [ "$HTTP_STATUS" = "200" ]; then
    echo "✓ 成功获取 traces 数据"
    # 尝试解析响应中的 traces 数量
    TRACE_COUNT=$(echo "$RESPONSE_BODY" | jq -r '.[0].result.data.json.traces | length' 2>/dev/null || echo "解析失败")
    echo "  找到 traces 数量: $TRACE_COUNT"
else
    echo "✗ 失败"
    echo "响应: $RESPONSE_BODY" | head -c 500
fi
echo ""

# 4.2 测试 sessions.all
echo "4.2 测试 sessions.all（最小参数）..."
# 最小参数：{"0":{"json":{"projectId":"xxx","filter":[],"orderBy":{"column":"createdAt","order":"desc"},"page":0,"limit":10}}}
SESSIONS_MINIMAL="%7B%220%22%3A%7B%22json%22%3A%7B%22projectId%22%3A%22$PROJECT_ID%22%2C%22filter%22%3A%5B%5D%2C%22orderBy%22%3A%7B%22column%22%3A%22createdAt%22%2C%22order%22%3A%22desc%22%7D%2C%22page%22%3A0%2C%22limit%22%3A10%7D%7D%7D"

SESSIONS_RESULT=$(curl -s -b "$COOKIE_FILE" \
  -H "Origin: $ORIGIN" \
  -H "Content-Type: application/json" \
  -w "HTTP_STATUS:%{http_code}" \
  "$BASE_URL/api/trpc/sessions.all?batch=1&input=$SESSIONS_MINIMAL")

HTTP_STATUS=$(echo "$SESSIONS_RESULT" | grep -o "HTTP_STATUS:[0-9]*" | cut -d: -f2)
RESPONSE_BODY=$(echo "$SESSIONS_RESULT" | sed 's/HTTP_STATUS:[0-9]*$//')

echo "状态码: $HTTP_STATUS"
if [ "$HTTP_STATUS" = "200" ]; then
    echo "✓ 成功获取 sessions 数据"
    SESSION_COUNT=$(echo "$RESPONSE_BODY" | jq -r '.[0].result.data.json | length' 2>/dev/null || echo "解析失败")
    echo "  找到 sessions 数量: $SESSION_COUNT"
else
    echo "✗ 失败"
    echo "响应: $RESPONSE_BODY" | head -c 500
fi
echo ""

# 4.3 测试 projects.byId
echo "4.3 测试 projects.byId..."
# 参数：{"0":{"json":{"projectId":"xxx"}}}
PROJECT_INPUT="%7B%220%22%3A%7B%22json%22%3A%7B%22projectId%22%3A%22$PROJECT_ID%22%7D%7D%7D"

PROJECT_RESULT=$(curl -s -b "$COOKIE_FILE" \
  -H "Origin: $ORIGIN" \
  -H "Content-Type: application/json" \
  -w "HTTP_STATUS:%{http_code}" \
  "$BASE_URL/api/trpc/projects.byId?batch=1&input=$PROJECT_INPUT")

HTTP_STATUS=$(echo "$PROJECT_RESULT" | grep -o "HTTP_STATUS:[0-9]*" | cut -d: -f2)
RESPONSE_BODY=$(echo "$PROJECT_RESULT" | sed 's/HTTP_STATUS:[0-9]*$//')

echo "状态码: $HTTP_STATUS"
if [ "$HTTP_STATUS" = "200" ]; then
    echo "✓ 成功获取项目信息"
    PROJECT_NAME=$(echo "$RESPONSE_BODY" | jq -r '.[0].result.data.json.name' 2>/dev/null || echo "解析失败")
    echo "  项目名称: $PROJECT_NAME"
else
    echo "✗ 失败"
    echo "响应: $RESPONSE_BODY" | head -c 500
fi
echo ""

# 清理
rm -f "$COOKIE_FILE"

echo "=== 测试完成 ==="
echo ""
echo "总结："
echo "- 如果状态码是 200，说明 CORS 和认证都工作正常"
echo "- 如果状态码是 400，说明参数格式有问题"
echo "- 如果状态码是 401，说明认证失败"
echo "- 如果状态码是 404，说明端点不存在"
