# 调试 tRPC 参数的脚本
Add-Type -AssemblyName System.Web

$ProjectId = "cmd2f20b00005p907gzt6ypdz"

Write-Host "=== tRPC 参数调试工具 ===" -ForegroundColor Green
Write-Host "Project ID: $ProjectId"
Write-Host ""

# 1. sessions.all 参数
Write-Host "1. sessions.all 正确参数:" -ForegroundColor Yellow
$sessionsParams = @{
    "0" = @{
        "json" = @{
            "projectId" = $ProjectId
            "filter" = @()
            "orderBy" = @{
                "column" = "createdAt"
                "order" = "desc"
            }
            "page" = 0
            "limit" = 10
        }
    }
}

$sessionsJson = $sessionsParams | ConvertTo-Json -Compress -Depth 10
$sessionsEncoded = [System.Web.HttpUtility]::UrlEncode($sessionsJson)

Write-Host "原始 JSON:"
Write-Host $sessionsJson -ForegroundColor Gray
Write-Host ""
Write-Host "URL 编码:"
Write-Host $sessionsEncoded -ForegroundColor Cyan
Write-Host ""
Write-Host "完整 URL:"
Write-Host "http://uat.ds.lesso.com:4000/api/trpc/sessions.all?batch=1&input=$sessionsEncoded" -ForegroundColor Green
Write-Host ""

# 2. traces.all 参数
Write-Host "2. traces.all 正确参数:" -ForegroundColor Yellow
$tracesParams = @{
    "0" = @{
        "json" = @{
            "projectId" = $ProjectId
            "searchQuery" = ""
            "searchType" = @()
            "filter" = @()
            "orderBy" = @{
                "column" = "timestamp"
                "order" = "desc"
            }
            "page" = 0
            "limit" = 10
        }
    }
}

$tracesJson = $tracesParams | ConvertTo-Json -Compress -Depth 10
$tracesEncoded = [System.Web.HttpUtility]::UrlEncode($tracesJson)

Write-Host "原始 JSON:"
Write-Host $tracesJson -ForegroundColor Gray
Write-Host ""
Write-Host "URL 编码:"
Write-Host $tracesEncoded -ForegroundColor Cyan
Write-Host ""
Write-Host "完整 URL:"
Write-Host "http://uat.ds.lesso.com:4000/api/trpc/traces.all?batch=1&input=$tracesEncoded" -ForegroundColor Green
Write-Host ""

# 3. projects.byId 参数
Write-Host "3. projects.byId 正确参数:" -ForegroundColor Yellow
$projectParams = @{
    "0" = @{
        "json" = @{
            "projectId" = $ProjectId
        }
    }
}

$projectJson = $projectParams | ConvertTo-Json -Compress -Depth 10
$projectEncoded = [System.Web.HttpUtility]::UrlEncode($projectJson)

Write-Host "原始 JSON:"
Write-Host $projectJson -ForegroundColor Gray
Write-Host ""
Write-Host "URL 编码:"
Write-Host $projectEncoded -ForegroundColor Cyan
Write-Host ""
Write-Host "完整 URL:"
Write-Host "http://uat.ds.lesso.com:4000/api/trpc/projects.byId?batch=1&input=$projectEncoded" -ForegroundColor Green
Write-Host ""

# 4. dashboard.chart 参数
Write-Host "4. dashboard.chart 正确参数:" -ForegroundColor Yellow
$dashboardParams = @{
    "0" = @{
        "json" = @{
            "projectId" = $ProjectId
            "from" = "traces"
            "select" = @("countTraces")
            "filter" = @()
            "groupBy" = @()
            "orderBy" = $null
        }
    }
}

$dashboardJson = $dashboardParams | ConvertTo-Json -Compress -Depth 10
$dashboardEncoded = [System.Web.HttpUtility]::UrlEncode($dashboardJson)

Write-Host "原始 JSON:"
Write-Host $dashboardJson -ForegroundColor Gray
Write-Host ""
Write-Host "URL 编码:"
Write-Host $dashboardEncoded -ForegroundColor Cyan
Write-Host ""
Write-Host "完整 URL:"
Write-Host "http://uat.ds.lesso.com:4000/api/trpc/dashboard.chart?batch=1&input=$dashboardEncoded" -ForegroundColor Green
Write-Host ""

# 5. 生成 curl 命令
Write-Host "5. 生成的 curl 命令:" -ForegroundColor Yellow
Write-Host ""

Write-Host "# sessions.all" -ForegroundColor Cyan
Write-Host "curl -b cookies.txt \\"
Write-Host "  -H `"Origin: http://localhost:8080`" \\"
Write-Host "  -H `"Content-Type: application/json`" \\"
Write-Host "  `"http://uat.ds.lesso.com:4000/api/trpc/sessions.all?batch=1&input=$sessionsEncoded`""
Write-Host ""

Write-Host "# traces.all" -ForegroundColor Cyan
Write-Host "curl -b cookies.txt \\"
Write-Host "  -H `"Origin: http://localhost:8080`" \\"
Write-Host "  -H `"Content-Type: application/json`" \\"
Write-Host "  `"http://uat.ds.lesso.com:4000/api/trpc/traces.all?batch=1&input=$tracesEncoded`""
Write-Host ""

Write-Host "# projects.byId" -ForegroundColor Cyan
Write-Host "curl -b cookies.txt \\"
Write-Host "  -H `"Origin: http://localhost:8080`" \\"
Write-Host "  -H `"Content-Type: application/json`" \\"
Write-Host "  `"http://uat.ds.lesso.com:4000/api/trpc/projects.byId?batch=1&input=$projectEncoded`""
Write-Host ""

Write-Host "=== 调试完成 ===" -ForegroundColor Green
